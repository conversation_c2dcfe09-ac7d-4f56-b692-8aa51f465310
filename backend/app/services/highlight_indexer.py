"""
Highlight Indexing Service (Parent Nodes)
Identifies, describes, and indexes key contextual moments as queryable entities
"""

import logging
import json
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.database import SessionLocal
from app.models.video import Video
from app.services.gemini_service import GeminiService

logger = logging.getLogger(__name__)

Base = declarative_base()

class VideoHighlight(Base):
    """Database model for video highlights"""
    __tablename__ = "video_highlights"
    
    id = Column(Integer, primary_key=True)
    video_id = Column(Integer, nullable=False)
    start_time = Column(Float, nullable=False)
    end_time = Column(Float, nullable=False)
    highlight_type = Column(String(50), nullable=False)  # 'key_moment', 'topic_change', 'emotional', etc.
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    keywords = Column(Text)  # JSON array of keywords
    importance_score = Column(Float, default=0.5)
    created_at = Column(DateTime)

@dataclass
class Highlight:
    video_id: int
    start_time: float
    end_time: float
    highlight_type: str
    title: str
    description: str
    keywords: List[str]
    importance_score: float

class HighlightIndexer:
    """
    Identifies and indexes key moments in videos for structured querying
    """
    
    def __init__(self, gemini_service=None):
        self.gemini_service = gemini_service
        self._setup_database()
        logger.info("🎯 Highlight indexer initialized")
    
    def _setup_database(self):
        """Setup highlights database"""
        try:
            # Create highlights table if it doesn't exist
            from app.core.database import engine
            Base.metadata.create_all(bind=engine)
            logger.info("✅ Highlights database ready")
        except Exception as e:
            logger.error(f"Error setting up highlights database: {e}")
    
    async def identify_highlights(self, video_id: int, force_reindex: bool = False) -> Dict:
        """
        Use Gemini to identify key moments and highlights in a video
        """
        start_time = time.time()
        
        try:
            # Check if already processed
            db = SessionLocal()
            if not force_reindex:
                existing = db.query(VideoHighlight).filter(VideoHighlight.video_id == video_id).first()
                if existing:
                    return {"status": "already_indexed", "video_id": video_id}
            
            # Get video data
            video = db.query(Video).filter(Video.id == video_id).first()
            if not video:
                return {"status": "video_not_found", "video_id": video_id}
            
            # Clear existing highlights if reindexing
            if force_reindex:
                db.query(VideoHighlight).filter(VideoHighlight.video_id == video_id).delete()
                db.commit()
            
            highlights = []
            
            # METHOD 1: Transcript-based highlights
            if video.transcript and self.gemini_service:
                transcript_highlights = await self._extract_transcript_highlights(video)
                highlights.extend(transcript_highlights)
            
            # METHOD 2: Section-based highlights
            if video.sections:
                section_highlights = self._extract_section_highlights(video)
                highlights.extend(section_highlights)
            
            # METHOD 3: Duration-based highlights (fallback)
            if not highlights and video.duration:
                duration_highlights = self._generate_duration_highlights(video)
                highlights.extend(duration_highlights)
            
            # Store highlights in database
            highlights_stored = 0
            for highlight in highlights:
                db_highlight = VideoHighlight(
                    video_id=highlight.video_id,
                    start_time=highlight.start_time,
                    end_time=highlight.end_time,
                    highlight_type=highlight.highlight_type,
                    title=highlight.title,
                    description=highlight.description,
                    keywords=json.dumps(highlight.keywords),
                    importance_score=highlight.importance_score
                )
                db.add(db_highlight)
                highlights_stored += 1
            
            db.commit()
            db.close()
            
            processing_time = time.time() - start_time
            logger.info(f"🎯 Identified {highlights_stored} highlights for video {video_id} in {processing_time:.2f}s")
            
            return {
                "status": "indexed",
                "video_id": video_id,
                "highlights_found": highlights_stored,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error identifying highlights for video {video_id}: {e}")
            return {"status": "error", "video_id": video_id, "error": str(e)}
    
    async def _extract_transcript_highlights(self, video: Video) -> List[Highlight]:
        """Extract highlights from transcript using Gemini"""
        highlights = []
        
        try:
            if not self.gemini_service:
                return highlights
            
            # Prepare transcript for analysis
            transcript_text = video.transcript
            if isinstance(transcript_text, list):
                transcript_text = ' '.join([entry.get('text', '') for entry in transcript_text])
            
            # Use Gemini to identify key moments
            prompt = f"""
            Analyze this video transcript and identify 5-8 key highlights or important moments.
            For each highlight, provide:
            1. Start time (estimate based on content flow)
            2. Duration (typically 30-60 seconds)
            3. Type (key_moment, topic_change, emotional, demonstration, etc.)
            4. Title (brief, descriptive)
            5. Description (what makes this moment important)
            6. Keywords (3-5 relevant search terms)
            7. Importance score (0.0-1.0)
            
            Transcript: {transcript_text[:3000]}...
            
            Return as JSON array with this structure:
            [{{
                "start_time": 120.0,
                "duration": 45.0,
                "type": "key_moment",
                "title": "Main Product Demo",
                "description": "Detailed demonstration of the core product features",
                "keywords": ["demo", "product", "features", "demonstration"],
                "importance": 0.9
            }}]
            """
            
            response = self.gemini_service.model.generate_content(prompt)
            
            if response and response.text:
                try:
                    # Parse JSON response
                    highlights_data = json.loads(response.text.strip())
                    
                    for item in highlights_data:
                        highlights.append(Highlight(
                            video_id=video.id,
                            start_time=item.get('start_time', 0),
                            end_time=item.get('start_time', 0) + item.get('duration', 30),
                            highlight_type=item.get('type', 'key_moment'),
                            title=item.get('title', 'Highlight'),
                            description=item.get('description', ''),
                            keywords=item.get('keywords', []),
                            importance_score=item.get('importance', 0.5)
                        ))
                        
                except json.JSONDecodeError:
                    logger.warning("Failed to parse Gemini highlights response as JSON")
            
        except Exception as e:
            logger.error(f"Error extracting transcript highlights: {e}")
        
        return highlights
    
    def _extract_section_highlights(self, video: Video) -> List[Highlight]:
        """Extract highlights from existing video sections"""
        highlights = []
        
        try:
            if not video.sections:
                return highlights
            
            sections = video.sections
            if isinstance(sections, str):
                sections = json.loads(sections)
            
            for i, section in enumerate(sections):
                start_time = section.get('start_time', i * 60)
                end_time = section.get('end_time', start_time + 60)
                title = section.get('title', f'Section {i+1}')
                description = section.get('description', '')
                
                # Extract keywords from title and description
                keywords = []
                text = f"{title} {description}".lower()
                # Simple keyword extraction
                important_words = [word for word in text.split() 
                                 if len(word) > 3 and word.isalpha()]
                keywords = list(set(important_words[:5]))
                
                highlights.append(Highlight(
                    video_id=video.id,
                    start_time=start_time,
                    end_time=end_time,
                    highlight_type='section',
                    title=title,
                    description=description,
                    keywords=keywords,
                    importance_score=0.7
                ))
                
        except Exception as e:
            logger.error(f"Error extracting section highlights: {e}")
        
        return highlights
    
    def _generate_duration_highlights(self, video: Video) -> List[Highlight]:
        """Generate basic highlights based on video duration (fallback)"""
        highlights = []
        
        try:
            duration = video.duration or 300
            
            # Create highlights at key intervals
            intervals = [
                (0.1, "Opening", "Video introduction and opening"),
                (0.3, "Early Content", "Initial content and setup"),
                (0.5, "Main Content", "Core content and main discussion"),
                (0.7, "Key Points", "Important points and details"),
                (0.9, "Conclusion", "Summary and closing remarks")
            ]
            
            for ratio, title, description in intervals:
                start_time = duration * ratio
                end_time = min(duration, start_time + 60)
                
                highlights.append(Highlight(
                    video_id=video.id,
                    start_time=start_time,
                    end_time=end_time,
                    highlight_type='duration_based',
                    title=title,
                    description=description,
                    keywords=[title.lower().replace(' ', '_')],
                    importance_score=0.4
                ))
                
        except Exception as e:
            logger.error(f"Error generating duration highlights: {e}")
        
        return highlights
    
    def search_highlights(self, video_id: int, query: str, max_results: int = 5) -> List[Dict]:
        """
        Search highlights for a specific video
        """
        start_time = time.time()
        results = []
        
        try:
            db = SessionLocal()
            query_lower = query.lower()
            
            # Search in titles, descriptions, and keywords
            highlights = db.query(VideoHighlight).filter(
                VideoHighlight.video_id == video_id
            ).all()
            
            for highlight in highlights:
                score = 0
                
                # Check title match
                if query_lower in highlight.title.lower():
                    score += 50
                
                # Check description match
                if query_lower in highlight.description.lower():
                    score += 30
                
                # Check keywords match
                keywords = json.loads(highlight.keywords or '[]')
                for keyword in keywords:
                    if query_lower in keyword.lower():
                        score += 20
                
                if score > 0:
                    results.append({
                        "timestamp": highlight.start_time,
                        "end_time": highlight.end_time,
                        "confidence": min(100, score),
                        "description": f"{highlight.title}: {highlight.description}",
                        "source": "highlight",
                        "highlight_type": highlight.highlight_type,
                        "importance": highlight.importance_score
                    })
            
            # Sort by confidence
            results.sort(key=lambda x: x["confidence"], reverse=True)
            
            processing_time = time.time() - start_time
            logger.info(f"🎯 Highlight search for '{query}' completed in {processing_time*1000:.1f}ms - found {len(results)} results")
            
            db.close()
            
        except Exception as e:
            logger.error(f"Highlight search error: {e}")
        
        return results[:max_results]
    
    def get_video_highlights(self, video_id: int) -> List[Dict]:
        """Get all highlights for a video"""
        try:
            db = SessionLocal()
            highlights = db.query(VideoHighlight).filter(
                VideoHighlight.video_id == video_id
            ).order_by(VideoHighlight.start_time).all()
            
            result = []
            for highlight in highlights:
                result.append({
                    "start_time": highlight.start_time,
                    "end_time": highlight.end_time,
                    "type": highlight.highlight_type,
                    "title": highlight.title,
                    "description": highlight.description,
                    "keywords": json.loads(highlight.keywords or '[]'),
                    "importance": highlight.importance_score
                })
            
            db.close()
            return result
            
        except Exception as e:
            logger.error(f"Error getting video highlights: {e}")
            return []

# Global instance
highlight_indexer = HighlightIndexer()
