"""
Transcript Indexing Service
Creates searchable index of transcript data with timestamps for instant text search
"""

import logging
import re
import json
import time
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.database import SessionLocal
from app.models.video import Video

logger = logging.getLogger(__name__)

class TranscriptSegment:
    def __init__(self, start_time: float, end_time: float, text: str, confidence: float = 1.0):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.confidence = confidence

class TranscriptIndexer:
    """
    Creates and manages searchable transcript indexes for instant text search
    """
    
    def __init__(self):
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you',
            'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
        logger.info("📚 Transcript indexer initialized")
    
    def parse_transcript_with_timestamps(self, transcript_data: List[Dict]) -> List[TranscriptSegment]:
        """
        Parse transcript data into searchable segments with timestamps
        """
        segments = []
        
        try:
            for entry in transcript_data:
                start_time = entry.get('start', 0)
                duration = entry.get('duration', 5)
                text = entry.get('text', '').strip()
                
                if text:
                    segments.append(TranscriptSegment(
                        start_time=start_time,
                        end_time=start_time + duration,
                        text=text,
                        confidence=1.0
                    ))
            
            logger.info(f"📝 Parsed {len(segments)} transcript segments")
            
        except Exception as e:
            logger.error(f"Error parsing transcript: {e}")
        
        return segments
    
    def create_word_index(self, segments: List[TranscriptSegment]) -> Dict[str, List[Tuple[float, str]]]:
        """
        Create word-to-timestamp index for instant lookup
        """
        word_index = {}
        
        for segment in segments:
            words = self._extract_words(segment.text)
            
            for word in words:
                if word not in self.stop_words and len(word) > 2:
                    if word not in word_index:
                        word_index[word] = []
                    
                    word_index[word].append((segment.start_time, segment.text))
        
        logger.info(f"📖 Created word index with {len(word_index)} unique words")
        return word_index
    
    def _extract_words(self, text: str) -> List[str]:
        """
        Extract and normalize words from text
        """
        # Remove punctuation and convert to lowercase
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Filter out very short words and numbers
        words = [word for word in words if len(word) > 2 and not word.isdigit()]
        
        return words
    
    def search_transcript(self, video_id: int, query: str) -> List[Dict]:
        """
        Lightning-fast transcript search using word index
        """
        start_time = time.time()
        results = []
        
        try:
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            
            if not video or not video.transcript:
                return results
            
            # Parse transcript if it's JSON
            transcript_data = []
            if isinstance(video.transcript, str):
                try:
                    transcript_data = json.loads(video.transcript)
                except:
                    # If it's plain text, create simple segments
                    transcript_data = [{"start": 0, "duration": 300, "text": video.transcript}]
            else:
                transcript_data = video.transcript
            
            # Parse into segments
            segments = self.parse_transcript_with_timestamps(transcript_data)
            
            # Create word index
            word_index = self.create_word_index(segments)
            
            # Search for query terms
            query_words = self._extract_words(query)
            matched_segments = set()
            
            # Exact phrase search first
            query_lower = query.lower()
            for segment in segments:
                if query_lower in segment.text.lower():
                    matched_segments.add(segment)
            
            # Individual word search
            for word in query_words:
                if word in word_index:
                    for timestamp, text in word_index[word]:
                        # Find the segment containing this timestamp
                        for segment in segments:
                            if segment.start_time <= timestamp <= segment.end_time:
                                matched_segments.add(segment)
                                break
            
            # Convert to results format
            for segment in matched_segments:
                # Calculate relevance score
                text_lower = segment.text.lower()
                score = 0
                
                # Exact phrase match gets highest score
                if query_lower in text_lower:
                    score = 100
                else:
                    # Score based on word matches
                    word_matches = sum(1 for word in query_words if word in text_lower)
                    score = (word_matches / len(query_words)) * 80
                
                results.append({
                    "timestamp": segment.start_time,
                    "confidence": score,
                    "description": f"'{query}' mentioned: {segment.text[:100]}...",
                    "source": "transcript",
                    "clip_start": max(0, segment.start_time - 5),
                    "clip_end": segment.end_time + 5,
                    "text": segment.text
                })
            
            # Sort by relevance
            results.sort(key=lambda x: x["confidence"], reverse=True)
            
            processing_time = time.time() - start_time
            logger.info(f"⚡ Transcript search for '{query}' completed in {processing_time*1000:.1f}ms - found {len(results)} results")
            
        except Exception as e:
            logger.error(f"Transcript search error: {e}")
        finally:
            db.close()
        
        return results[:10]  # Return top 10 matches
    
    def index_video_transcript(self, video_id: int) -> Dict:
        """
        Pre-index a video's transcript for faster future searches
        """
        try:
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            
            if not video or not video.transcript:
                return {"status": "no_transcript", "video_id": video_id}
            
            # Parse and index transcript
            transcript_data = video.transcript
            if isinstance(transcript_data, str):
                try:
                    transcript_data = json.loads(transcript_data)
                except:
                    transcript_data = [{"start": 0, "duration": 300, "text": transcript_data}]
            
            segments = self.parse_transcript_with_timestamps(transcript_data)
            word_index = self.create_word_index(segments)
            
            # Store index in database (you could create a separate table for this)
            # For now, we'll just return the stats
            
            return {
                "status": "indexed",
                "video_id": video_id,
                "segments": len(segments),
                "unique_words": len(word_index),
                "total_duration": segments[-1].end_time if segments else 0
            }
            
        except Exception as e:
            logger.error(f"Error indexing transcript for video {video_id}: {e}")
            return {"status": "error", "video_id": video_id, "error": str(e)}
        finally:
            db.close()

# Global instance
transcript_indexer = TranscriptIndexer()
