"""
Comprehensive Video Indexer
Orchestrates indexing of all tree components: ROOT, PARENT, and LEAF nodes
"""

import logging
import asyncio
import time
from typing import Dict, List
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.models.video import Video
from app.services.fast_transcript_search import fast_transcript_search
from app.services.visual_embeddings_index import visual_embeddings_index
from app.services.highlight_indexer import highlight_indexer
from app.services.gemini_service import GeminiService

logger = logging.getLogger(__name__)

class ComprehensiveIndexer:
    """
    Orchestrates complete video indexing for tree-based search
    """
    
    def __init__(self, gemini_service=None):
        self.gemini_service = gemini_service
        logger.info("📚 Comprehensive indexer initialized")
    
    async def index_video_complete(self, video_id: int, force_reindex: bool = False) -> Dict:
        """
        Index a video across all tree levels for optimal search performance
        """
        start_time = time.time()
        results = {
            "video_id": video_id,
            "status": "started",
            "components": {}
        }
        
        try:
            # Get video info
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            
            if not video:
                return {"status": "video_not_found", "video_id": video_id}
            
            logger.info(f"🚀 Starting comprehensive indexing for video {video_id}: {video.title}")
            
            # LEAF NODES: Text indexing (transcript)
            if video.transcript:
                logger.info(f"📝 Indexing transcript for video {video_id}")
                transcript_result = fast_transcript_search.index_video_transcript(
                    video_id, force_reindex
                )
                results["components"]["transcript"] = transcript_result
                logger.info(f"✅ Transcript indexing: {transcript_result.get('status')}")
            else:
                results["components"]["transcript"] = {"status": "no_transcript"}
                logger.info(f"⚠️ No transcript available for video {video_id}")
            
            # LEAF NODES: Visual embeddings indexing
            logger.info(f"🖼️ Indexing visual embeddings for video {video_id}")
            visual_embeddings_index.gemini_service = self.gemini_service
            visual_result = await visual_embeddings_index.index_video_frames(
                video_id, force_reindex
            )
            results["components"]["visual_embeddings"] = visual_result
            logger.info(f"✅ Visual embeddings: {visual_result.get('status')}")
            
            # PARENT NODES: Highlights indexing
            logger.info(f"🎯 Identifying highlights for video {video_id}")
            highlight_indexer.gemini_service = self.gemini_service
            highlights_result = await highlight_indexer.identify_highlights(
                video_id, force_reindex
            )
            results["components"]["highlights"] = highlights_result
            logger.info(f"✅ Highlights indexing: {highlights_result.get('status')}")
            
            # ROOT NODE: Video summary (if not exists)
            if not video.sections or force_reindex:
                logger.info(f"📋 Generating video summary for video {video_id}")
                summary_result = await self._generate_video_summary(video)
                results["components"]["summary"] = summary_result
                logger.info(f"✅ Video summary: {summary_result.get('status')}")
            else:
                results["components"]["summary"] = {"status": "already_exists"}
            
            db.close()
            
            # Calculate overall status
            component_statuses = [
                comp.get("status") for comp in results["components"].values()
            ]
            
            if all(status in ["indexed", "already_indexed", "already_exists"] for status in component_statuses):
                results["status"] = "completed"
            elif any(status == "error" for status in component_statuses):
                results["status"] = "partial_failure"
            else:
                results["status"] = "completed_with_warnings"
            
            total_time = time.time() - start_time
            results["processing_time"] = total_time
            
            logger.info(f"🎉 Comprehensive indexing completed for video {video_id} in {total_time:.2f}s")
            logger.info(f"📊 Status: {results['status']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive indexing for video {video_id}: {e}")
            return {
                "status": "error",
                "video_id": video_id,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    async def _generate_video_summary(self, video: Video) -> Dict:
        """
        Generate ROOT NODE: Overall video description and summary
        """
        try:
            if not self.gemini_service:
                return {"status": "no_gemini_service"}
            
            # Create comprehensive video summary
            summary_prompt = f"""
            Create a comprehensive summary for this video that will help with search and discovery.
            
            Video Title: {video.title}
            Duration: {video.duration or 'Unknown'} seconds
            Video Type: {video.video_type}
            
            """
            
            # Add transcript context if available
            if video.transcript:
                transcript_text = video.transcript
                if isinstance(transcript_text, list):
                    transcript_text = ' '.join([entry.get('text', '') for entry in transcript_text])
                
                summary_prompt += f"""
                Transcript Sample: {transcript_text[:1000]}...
                
                Based on this information, provide:
                1. Main topics covered (3-5 key topics)
                2. Video category/genre
                3. Target audience
                4. Key concepts and entities mentioned
                5. Overall tone and style
                6. Searchable keywords (10-15 terms)
                
                Format as JSON:
                {{
                    "main_topics": ["topic1", "topic2", ...],
                    "category": "category_name",
                    "target_audience": "audience_description",
                    "key_concepts": ["concept1", "concept2", ...],
                    "tone": "tone_description",
                    "keywords": ["keyword1", "keyword2", ...]
                }}
                """
            else:
                summary_prompt += """
                No transcript available. Based on the title and metadata, provide a basic summary structure.
                """
            
            response = self.gemini_service.model.generate_content(summary_prompt)
            
            if response and response.text:
                # Store summary in video sections if not already present
                try:
                    import json
                    summary_data = json.loads(response.text.strip())
                    
                    # Update video with summary information
                    db = SessionLocal()
                    video_obj = db.query(Video).filter(Video.id == video.id).first()
                    if video_obj:
                        # Store as sections if no sections exist
                        if not video_obj.sections:
                            video_obj.sections = [summary_data]
                        db.commit()
                    db.close()
                    
                    return {"status": "generated", "summary": summary_data}
                    
                except json.JSONDecodeError:
                    logger.warning("Failed to parse Gemini summary as JSON")
                    return {"status": "generated_text_only", "summary": response.text}
            
            return {"status": "no_response"}
            
        except Exception as e:
            logger.error(f"Error generating video summary: {e}")
            return {"status": "error", "error": str(e)}
    
    def get_indexing_status(self, video_id: int) -> Dict:
        """
        Get comprehensive indexing status for a video
        """
        try:
            status = {
                "video_id": video_id,
                "components": {}
            }
            
            # Check transcript indexing
            transcript_stats = fast_transcript_search.get_video_stats(video_id)
            status["components"]["transcript"] = transcript_stats
            
            # Check visual embeddings
            visual_stats = visual_embeddings_index.get_video_embedding_stats(video_id)
            status["components"]["visual_embeddings"] = visual_stats
            
            # Check highlights
            highlights = highlight_indexer.get_video_highlights(video_id)
            status["components"]["highlights"] = {
                "indexed": len(highlights) > 0,
                "count": len(highlights)
            }
            
            # Check video summary
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            status["components"]["summary"] = {
                "exists": bool(video and video.sections)
            }
            db.close()
            
            # Overall status
            indexed_components = sum(1 for comp in status["components"].values() 
                                   if comp.get("indexed") or comp.get("exists"))
            total_components = len(status["components"])
            
            status["overall"] = {
                "indexed_components": indexed_components,
                "total_components": total_components,
                "completion_percentage": (indexed_components / total_components) * 100
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting indexing status: {e}")
            return {"error": str(e)}
    
    async def reindex_video(self, video_id: int) -> Dict:
        """
        Force reindex all components of a video
        """
        logger.info(f"🔄 Force reindexing video {video_id}")
        return await self.index_video_complete(video_id, force_reindex=True)

# Global instance
comprehensive_indexer = ComprehensiveIndexer()
