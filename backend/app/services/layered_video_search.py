"""
Complete Tree-Based Video Search Engine
Implements the expert-recommended tree approach with all node types:

ROOT NODES: Overall video descriptions and summaries
PARENT NODES: Structured highlights and key moments
LEAF NODES:
  - Text: Fast indexed transcript search (milliseconds)
  - Visual: Local embeddings via ChromaDB (milliseconds)

HYBRID STRATEGIES: Intelligent combination of all search methods
"""

import logging
import asyncio
import time
import re
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.core.database import SessionLocal
from app.models.video import Video, VideoFrame
from app.services.gemini_service import GeminiService
from app.services.native_video_search import NativeVideoSearchService
from app.services.fast_transcript_search import fast_transcript_search
from app.services.visual_embeddings_index import visual_embeddings_index
from app.services.highlight_indexer import highlight_indexer

logger = logging.getLogger(__name__)

class SearchResult:
    def __init__(self, timestamp: float, confidence: float, description: str, 
                 source: str, clip_start: float = None, clip_end: float = None):
        self.timestamp = timestamp
        self.confidence = confidence
        self.description = description
        self.source = source  # "transcript", "visual", "gemini", "frame"
        self.clip_start = clip_start or max(0, timestamp - 5)
        self.clip_end = clip_end or timestamp + 10

class LayeredVideoSearch:
    """
    Implements the optimal layered search strategy for instant results
    """
    
    def __init__(self, gemini_service=None, native_service=None):
        self.gemini_service = gemini_service
        self.native_service = native_service
        
        # Query classification patterns
        self.visual_indicators = [
            'red', 'blue', 'green', 'yellow', 'color', 'wearing', 'holding',
            'person', 'people', 'face', 'hand', 'object', 'car', 'building',
            'screen', 'text', 'sign', 'logo', 'background', 'scene'
        ]
        
        self.text_indicators = [
            'said', 'mentioned', 'talked about', 'discussed', 'explained',
            'quote', 'word', 'phrase', 'topic', 'subject', 'conversation'
        ]
        
        logger.info("🔄 Layered video search initialized")
    
    def _classify_query(self, query: str) -> str:
        """
        Classify query as 'text', 'visual', or 'mixed' for optimal routing
        """
        query_lower = query.lower()
        
        visual_score = sum(1 for indicator in self.visual_indicators if indicator in query_lower)
        text_score = sum(1 for indicator in self.text_indicators if indicator in query_lower)
        
        if text_score > visual_score:
            return "text"
        elif visual_score > text_score:
            return "visual"
        else:
            return "mixed"
    
    async def _search_transcript_local(self, video_id: int, query: str) -> List[SearchResult]:
        """
        TEXT LEAF NODES: Lightning-fast indexed transcript search (milliseconds)
        """
        start_time = time.time()
        results = []

        try:
            # Use fast transcript search with FTS5 indexing
            transcript_results = fast_transcript_search.search(video_id, query, max_results=10)

            for result in transcript_results:
                results.append(SearchResult(
                    timestamp=result.start_time,
                    confidence=result.relevance_score,
                    description=f"Transcript: {result.text[:100]}...",
                    source="transcript_indexed",
                    clip_start=result.start_time,
                    clip_end=result.end_time
                ))

            processing_time = time.time() - start_time
            logger.info(f"⚡ Fast transcript search completed in {processing_time*1000:.1f}ms - found {len(results)} results")

        except Exception as e:
            logger.error(f"Fast transcript search error: {e}")

        return results
    
    async def _search_visual_local(self, video_id: int, query: str) -> List[SearchResult]:
        """
        VISUAL LEAF NODES: Lightning-fast local visual embeddings search (milliseconds)
        """
        start_time = time.time()
        results = []

        try:
            # Use visual embeddings index for instant similarity search
            visual_results = await visual_embeddings_index.search_visual_similarity(
                video_id, query, max_results=10
            )

            for result in visual_results:
                results.append(SearchResult(
                    timestamp=result.timestamp,
                    confidence=result.similarity_score,
                    description=f"Visual: {result.description}",
                    source="visual_embeddings",
                    clip_start=max(0, result.timestamp - 5),
                    clip_end=result.timestamp + 10
                ))

            # Fallback to frame description search if no embeddings
            if not results:
                db = SessionLocal()
                frames = db.query(VideoFrame).filter(
                    VideoFrame.video_id == video_id,
                    VideoFrame.description.ilike(f"%{query}%")
                ).limit(5).all()

                for frame in frames:
                    if frame.description:
                        results.append(SearchResult(
                            timestamp=frame.timestamp,
                            confidence=75.0,
                            description=f"Frame: {frame.description}",
                            source="visual_description"
                        ))
                db.close()

            processing_time = time.time() - start_time
            logger.info(f"⚡ Visual search completed in {processing_time*1000:.1f}ms - found {len(results)} results")

        except Exception as e:
            logger.error(f"Visual search error: {e}")

        return results

    async def _search_highlights(self, video_id: int, query: str) -> List[SearchResult]:
        """
        PARENT NODES: Search structured highlights and key moments
        """
        start_time = time.time()
        results = []

        try:
            # Search highlights using the highlight indexer
            highlight_results = highlight_indexer.search_highlights(video_id, query, max_results=5)

            for result in highlight_results:
                results.append(SearchResult(
                    timestamp=result["timestamp"],
                    confidence=result["confidence"],
                    description=f"Highlight: {result['description']}",
                    source="highlight",
                    clip_start=result["timestamp"],
                    clip_end=result.get("end_time", result["timestamp"] + 30)
                ))

            processing_time = time.time() - start_time
            logger.info(f"🎯 Highlight search completed in {processing_time*1000:.1f}ms - found {len(results)} results")

        except Exception as e:
            logger.error(f"Highlight search error: {e}")

        return results

    async def _search_gemini_smart(self, video_path: str, query: str) -> List[SearchResult]:
        """
        STEP 2: Smart Gemini whole-video analysis (seconds)
        """
        start_time = time.time()
        results = []
        
        if not self.native_service:
            return results
        
        try:
            logger.info(f"🧠 Using Gemini smart search for complex query: '{query}'")
            
            # Use native video search for complex understanding
            clips = await self.native_service.search_visual_content(
                video_path=video_path,
                query=query,
                search_type="comprehensive"
            )
            
            for clip in clips:
                results.append(SearchResult(
                    timestamp=clip.start_time,
                    confidence=clip.confidence,
                    description=clip.description,
                    source="gemini",
                    clip_start=clip.start_time,
                    clip_end=clip.end_time
                ))
            
            processing_time = time.time() - start_time
            logger.info(f"🧠 Gemini search completed in {processing_time:.1f}s - found {len(results)} results")
            
        except Exception as e:
            logger.error(f"Gemini search error: {e}")
        
        return results
    
    async def _search_frame_fallback(self, video_path: str, query: str, video_id: int) -> List[SearchResult]:
        """
        STEP 3: Minimal frame sampling fallback (minutes)
        """
        start_time = time.time()
        results = []
        
        if not self.gemini_service:
            return results
        
        try:
            logger.info(f"🔄 Using frame fallback search for: '{query}'")
            
            # Ultra-minimal sampling - just 3 frames for speed
            import cv2
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return results
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            # Sample just 3 frames: beginning, middle, end
            sample_times = [duration * 0.2, duration * 0.5, duration * 0.8]
            
            for timestamp in sample_times:
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                if ret:
                    # Quick frame analysis
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as tmp:
                        cv2.imwrite(tmp.name, frame)
                        
                        analysis = await self.gemini_service.analyze_frame_for_search(tmp.name, query)
                        
                        if analysis.get("match", False):
                            results.append(SearchResult(
                                timestamp=timestamp,
                                confidence=analysis.get("confidence", 0.5) * 100,
                                description=analysis.get("description", f"Found '{query}' at {timestamp:.1f}s"),
                                source="frame"
                            ))
            
            cap.release()
            
            processing_time = time.time() - start_time
            logger.info(f"🔄 Frame fallback completed in {processing_time:.1f}s - found {len(results)} results")
            
        except Exception as e:
            logger.error(f"Frame fallback error: {e}")
        
        return results
    
    async def search(self, video_id: int, video_path: str, query: str) -> Dict:
        """
        Complete Tree-Based Search: ROOT → PARENT → LEAF NODES
        """
        start_time = time.time()
        all_results = []
        search_path = []

        # Classify query for optimal routing
        query_type = self._classify_query(query)
        logger.info(f"🔍 Tree search for '{query}' (type: {query_type})")

        # LEAF NODES: Fast local search (milliseconds)
        if query_type in ["text", "mixed"]:
            transcript_results = await self._search_transcript_local(video_id, query)
            all_results.extend(transcript_results)
            if transcript_results:
                search_path.append("transcript_indexed")

        if query_type in ["visual", "mixed"]:
            visual_results = await self._search_visual_local(video_id, query)
            all_results.extend(visual_results)
            if visual_results:
                search_path.append("visual_embeddings")

        # PARENT NODES: Structured highlights search (milliseconds)
        highlight_results = await self._search_highlights(video_id, query)
        all_results.extend(highlight_results)
        if highlight_results:
            search_path.append("highlights")

        # ROOT NODE: Gemini whole-video analysis (only if needed)
        if len(all_results) < 3 or query_type == "mixed":
            gemini_results = await self._search_gemini_smart(video_path, query)
            all_results.extend(gemini_results)
            if gemini_results:
                search_path.append("gemini_root")

        # FALLBACK: Minimal frame sampling (only if no other results)
        if len(all_results) == 0:
            frame_results = await self._search_frame_fallback(video_path, query, video_id)
            all_results.extend(frame_results)
            if frame_results:
                search_path.append("frame_fallback")

        # Intelligent result merging and deduplication
        unique_results = self._merge_and_deduplicate_results(all_results)

        total_time = time.time() - start_time

        return {
            "results": unique_results[:10],  # Top 10 results
            "total_results": len(unique_results),
            "processing_time": total_time,
            "search_path": search_path,
            "query_type": query_type,
            "method": "tree_based_search",
            "tree_coverage": {
                "leaf_nodes": len([p for p in search_path if p in ["transcript_indexed", "visual_embeddings"]]),
                "parent_nodes": len([p for p in search_path if p == "highlights"]),
                "root_nodes": len([p for p in search_path if p == "gemini_root"])
            }
        }

    def _merge_and_deduplicate_results(self, all_results: List[SearchResult]) -> List[SearchResult]:
        """
        Intelligently merge and deduplicate results from different tree levels
        """
        # Group results by timestamp proximity (within 10 seconds)
        groups = []

        for result in sorted(all_results, key=lambda x: x.timestamp):
            placed = False

            for group in groups:
                # Check if this result belongs to an existing group
                if any(abs(result.timestamp - existing.timestamp) < 10 for existing in group):
                    group.append(result)
                    placed = True
                    break

            if not placed:
                groups.append([result])

        # Select best result from each group
        unique_results = []
        for group in groups:
            # Prioritize by source quality: highlights > transcript > visual > gemini > frame
            source_priority = {
                "highlight": 5,
                "transcript_indexed": 4,
                "visual_embeddings": 3,
                "gemini": 2,
                "frame": 1
            }

            best_result = max(group, key=lambda x: (
                source_priority.get(x.source, 0),
                x.confidence
            ))

            unique_results.append(best_result)

        # Sort by confidence
        return sorted(unique_results, key=lambda x: x.confidence, reverse=True)
