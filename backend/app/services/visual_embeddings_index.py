"""
Local Visual Embeddings Index (Visual Leaf Nodes)
Pre-computed, locally indexed vector database for instant visual similarity search
"""

import logging
import numpy as np
import json
import time
import cv2
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import tempfile
import asyncio

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logging.warning("ChromaDB not available - visual embeddings will be disabled")

from app.services.gemini_service import GeminiService
from app.models.video import Video, VideoFrame
from app.core.database import SessionLocal

logger = logging.getLogger(__name__)

class VisualSearchResult:
    def __init__(self, video_id: int, frame_id: int, timestamp: float, 
                 similarity_score: float, description: str, frame_path: str = ""):
        self.video_id = video_id
        self.frame_id = frame_id
        self.timestamp = timestamp
        self.similarity_score = similarity_score
        self.description = description
        self.frame_path = frame_path

class VisualEmbeddingsIndex:
    """
    High-performance visual search using pre-computed embeddings and ChromaDB
    """
    
    def __init__(self, collection_name: str = "video_frames", gemini_service=None):
        self.collection_name = collection_name
        self.gemini_service = gemini_service
        self.client = None
        self.collection = None
        
        if CHROMADB_AVAILABLE:
            self._initialize_chromadb()
        else:
            logger.warning("⚠️ ChromaDB not available - visual search will use fallback")
        
        logger.info(f"🖼️ Visual embeddings index initialized")
    
    def _initialize_chromadb(self):
        """Initialize ChromaDB for vector storage"""
        try:
            # Create persistent ChromaDB client
            self.client = chromadb.PersistentClient(
                path="./chroma_db",
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"✅ Connected to existing ChromaDB collection: {self.collection_name}")
            except:
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "Video frame embeddings for visual search"}
                )
                logger.info(f"✅ Created new ChromaDB collection: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            self.client = None
            self.collection = None
    
    async def _generate_frame_embedding(self, frame_path: str, description: str = "") -> Optional[List[float]]:
        """
        Generate embedding for a frame using Gemini
        """
        try:
            if not self.gemini_service:
                return None
            
            # Use Gemini to generate embedding
            # This is a placeholder - you'd use actual embedding generation
            # For now, we'll create a simple text-based embedding from description
            if description:
                # Simple text embedding simulation
                words = description.lower().split()
                embedding = [hash(word) % 1000 / 1000.0 for word in words[:384]]
                # Pad or truncate to 384 dimensions
                while len(embedding) < 384:
                    embedding.append(0.0)
                return embedding[:384]
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None
    
    async def index_video_frames(self, video_id: int, force_reindex: bool = False) -> Dict:
        """
        Index all frames of a video for visual search
        """
        start_time = time.time()
        
        if not CHROMADB_AVAILABLE or not self.collection:
            return {"status": "chromadb_unavailable", "video_id": video_id}
        
        try:
            # Check if already indexed
            if not force_reindex:
                existing = self.collection.get(
                    where={"video_id": video_id},
                    limit=1
                )
                if existing['ids']:
                    return {"status": "already_indexed", "video_id": video_id}
            
            # Get video frames from database
            db = SessionLocal()
            frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()
            
            if not frames:
                return {"status": "no_frames", "video_id": video_id}
            
            # Clear existing embeddings if reindexing
            if force_reindex:
                existing_ids = self.collection.get(
                    where={"video_id": video_id}
                )['ids']
                if existing_ids:
                    self.collection.delete(ids=existing_ids)
            
            # Process frames in batches for efficiency
            batch_size = 10
            frames_indexed = 0
            
            for i in range(0, len(frames), batch_size):
                batch = frames[i:i + batch_size]
                
                # Prepare batch data
                ids = []
                embeddings = []
                metadatas = []
                documents = []
                
                for frame in batch:
                    # Generate embedding
                    embedding = await self._generate_frame_embedding(
                        frame.frame_path, 
                        frame.description or ""
                    )
                    
                    if embedding:
                        frame_id = f"video_{video_id}_frame_{frame.id}"
                        ids.append(frame_id)
                        embeddings.append(embedding)
                        
                        metadatas.append({
                            "video_id": video_id,
                            "frame_id": frame.id,
                            "timestamp": frame.timestamp,
                            "frame_path": frame.frame_path
                        })
                        
                        documents.append(frame.description or f"Frame at {frame.timestamp}s")
                        frames_indexed += 1
                
                # Add batch to ChromaDB
                if ids:
                    self.collection.add(
                        ids=ids,
                        embeddings=embeddings,
                        metadatas=metadatas,
                        documents=documents
                    )
            
            db.close()
            
            processing_time = time.time() - start_time
            logger.info(f"🖼️ Indexed {frames_indexed} frame embeddings for video {video_id} in {processing_time:.2f}s")
            
            return {
                "status": "indexed",
                "video_id": video_id,
                "frames_indexed": frames_indexed,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error indexing visual embeddings for video {video_id}: {e}")
            return {"status": "error", "video_id": video_id, "error": str(e)}
    
    async def search_visual_similarity(self, video_id: int, query: str, 
                                     max_results: int = 10) -> List[VisualSearchResult]:
        """
        Search for visually similar frames using embeddings
        """
        start_time = time.time()
        results = []
        
        if not CHROMADB_AVAILABLE or not self.collection:
            return results
        
        try:
            # Generate query embedding
            query_embedding = await self._generate_frame_embedding("", query)
            
            if not query_embedding:
                return results
            
            # Search ChromaDB
            search_results = self.collection.query(
                query_embeddings=[query_embedding],
                where={"video_id": video_id},
                n_results=max_results
            )
            
            # Convert to VisualSearchResult objects
            if search_results['ids'] and search_results['ids'][0]:
                for i, frame_id in enumerate(search_results['ids'][0]):
                    metadata = search_results['metadatas'][0][i]
                    distance = search_results['distances'][0][i]
                    document = search_results['documents'][0][i]
                    
                    # Convert distance to similarity score (0-100)
                    similarity_score = max(0, 100 - (distance * 100))
                    
                    results.append(VisualSearchResult(
                        video_id=metadata['video_id'],
                        frame_id=metadata['frame_id'],
                        timestamp=metadata['timestamp'],
                        similarity_score=similarity_score,
                        description=document,
                        frame_path=metadata.get('frame_path', '')
                    ))
            
            processing_time = time.time() - start_time
            logger.info(f"⚡ Visual similarity search for '{query}' completed in {processing_time*1000:.1f}ms - found {len(results)} results")
            
        except Exception as e:
            logger.error(f"Visual similarity search error: {e}")
        
        return results
    
    def get_collection_stats(self) -> Dict:
        """Get statistics about the visual embeddings collection"""
        if not CHROMADB_AVAILABLE or not self.collection:
            return {"available": False}
        
        try:
            count = self.collection.count()
            return {
                "available": True,
                "total_embeddings": count,
                "collection_name": self.collection_name
            }
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {"available": False, "error": str(e)}
    
    def get_video_embedding_stats(self, video_id: int) -> Dict:
        """Get embedding statistics for a specific video"""
        if not CHROMADB_AVAILABLE or not self.collection:
            return {"indexed": False}
        
        try:
            results = self.collection.get(
                where={"video_id": video_id}
            )
            
            if results['ids']:
                timestamps = [meta['timestamp'] for meta in results['metadatas']]
                return {
                    "indexed": True,
                    "frame_count": len(results['ids']),
                    "time_range": {
                        "start": min(timestamps),
                        "end": max(timestamps)
                    }
                }
            else:
                return {"indexed": False}
                
        except Exception as e:
            logger.error(f"Error getting video embedding stats: {e}")
            return {"indexed": False, "error": str(e)}

# Global instance
visual_embeddings_index = VisualEmbeddingsIndex()
