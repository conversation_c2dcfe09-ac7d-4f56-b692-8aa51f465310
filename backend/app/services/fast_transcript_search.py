"""
Fast Indexed Transcript Search (Text Leaf Nodes)
Implements high-performance text search with full-text indexing and timestamp precision
"""

import logging
import sqlite3
import json
import re
import time
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.models.video import Video

logger = logging.getLogger(__name__)

class TranscriptSearchResult:
    def __init__(self, video_id: int, start_time: float, end_time: float, 
                 text: str, relevance_score: float, context: str = ""):
        self.video_id = video_id
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.relevance_score = relevance_score
        self.context = context

class FastTranscriptSearch:
    """
    High-performance transcript search using SQLite FTS5 for instant text queries
    """
    
    def __init__(self, db_path: str = "transcript_index.db"):
        self.db_path = Path(db_path)
        self.conn = None
        self._initialize_db()
        logger.info(f"📚 Fast transcript search initialized: {self.db_path}")
    
    def _initialize_db(self):
        """Initialize SQLite database with FTS5 for full-text search"""
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.execute("PRAGMA journal_mode=WAL")  # Better concurrency
            
            # Create FTS5 table for transcript segments
            self.conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS transcript_fts USING fts5(
                    video_id,
                    start_time,
                    end_time,
                    text,
                    normalized_text,
                    content='transcript_segments',
                    content_rowid='id'
                )
            """)
            
            # Create backing table
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS transcript_segments (
                    id INTEGER PRIMARY KEY,
                    video_id INTEGER NOT NULL,
                    start_time REAL NOT NULL,
                    end_time REAL NOT NULL,
                    text TEXT NOT NULL,
                    normalized_text TEXT NOT NULL,
                    word_count INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for performance
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_video_time ON transcript_segments(video_id, start_time)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_video_id ON transcript_segments(video_id)")
            
            self.conn.commit()
            logger.info("✅ Transcript search database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize transcript database: {e}")
            raise
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for better search matching"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Remove filler words for better matching
        filler_words = ['um', 'uh', 'er', 'ah', 'like', 'you know', 'so', 'well']
        words = text.split()
        words = [w for w in words if w not in filler_words]
        
        return ' '.join(words)
    
    def index_video_transcript(self, video_id: int, force_reindex: bool = False) -> Dict:
        """
        Index a video's transcript for fast searching
        """
        start_time = time.time()
        
        try:
            # Check if already indexed
            if not force_reindex:
                cursor = self.conn.execute(
                    "SELECT COUNT(*) FROM transcript_segments WHERE video_id = ?",
                    (video_id,)
                )
                if cursor.fetchone()[0] > 0:
                    return {"status": "already_indexed", "video_id": video_id}
            
            # Get video transcript
            db = SessionLocal()
            video = db.query(Video).filter(Video.id == video_id).first()
            
            if not video or not video.transcript:
                return {"status": "no_transcript", "video_id": video_id}
            
            # Parse transcript data
            transcript_data = video.transcript
            if isinstance(transcript_data, str):
                try:
                    transcript_data = json.loads(transcript_data)
                except:
                    # Plain text - create single segment
                    transcript_data = [{
                        "start": 0,
                        "duration": video.duration or 300,
                        "text": transcript_data
                    }]
            
            # Clear existing data if reindexing
            if force_reindex:
                self.conn.execute("DELETE FROM transcript_segments WHERE video_id = ?", (video_id,))
                self.conn.execute("DELETE FROM transcript_fts WHERE video_id = ?", (video_id,))
            
            # Index transcript segments
            segments_indexed = 0
            for entry in transcript_data:
                start = entry.get('start', 0)
                duration = entry.get('duration', 5)
                text = entry.get('text', '').strip()
                
                if text and len(text) > 3:  # Skip very short segments
                    end_time = start + duration
                    normalized_text = self._normalize_text(text)
                    word_count = len(text.split())
                    
                    # Insert into backing table
                    cursor = self.conn.execute("""
                        INSERT INTO transcript_segments 
                        (video_id, start_time, end_time, text, normalized_text, word_count)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (video_id, start, end_time, text, normalized_text, word_count))
                    
                    # Insert into FTS table
                    self.conn.execute("""
                        INSERT INTO transcript_fts 
                        (video_id, start_time, end_time, text, normalized_text)
                        VALUES (?, ?, ?, ?, ?)
                    """, (video_id, start, end_time, text, normalized_text))
                    
                    segments_indexed += 1
            
            self.conn.commit()
            db.close()
            
            processing_time = time.time() - start_time
            logger.info(f"📚 Indexed {segments_indexed} transcript segments for video {video_id} in {processing_time:.2f}s")
            
            return {
                "status": "indexed",
                "video_id": video_id,
                "segments_indexed": segments_indexed,
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"Error indexing transcript for video {video_id}: {e}")
            return {"status": "error", "video_id": video_id, "error": str(e)}
    
    def search(self, video_id: int, query: str, max_results: int = 10) -> List[TranscriptSearchResult]:
        """
        Lightning-fast transcript search using FTS5
        """
        start_time = time.time()
        results = []
        
        try:
            # Prepare search query for FTS5
            # Handle phrases and individual words
            if '"' in query:
                # Phrase search
                fts_query = query
            else:
                # Individual word search with OR
                words = query.lower().split()
                fts_query = ' OR '.join(words)
            
            # Search using FTS5
            cursor = self.conn.execute("""
                SELECT ts.video_id, ts.start_time, ts.end_time, ts.text, 
                       bm25(transcript_fts) as relevance_score
                FROM transcript_fts 
                JOIN transcript_segments ts ON ts.id = transcript_fts.rowid
                WHERE transcript_fts MATCH ? AND ts.video_id = ?
                ORDER BY relevance_score ASC
                LIMIT ?
            """, (fts_query, video_id, max_results))
            
            for row in cursor.fetchall():
                video_id, start_time, end_time, text, relevance_score = row
                
                # Convert BM25 score to percentage (lower is better in BM25)
                confidence = max(0, 100 - abs(relevance_score * 10))
                
                results.append(TranscriptSearchResult(
                    video_id=video_id,
                    start_time=start_time,
                    end_time=end_time,
                    text=text,
                    relevance_score=confidence
                ))
            
            processing_time = time.time() - start_time
            logger.info(f"⚡ Transcript search for '{query}' completed in {processing_time*1000:.1f}ms - found {len(results)} results")
            
        except Exception as e:
            logger.error(f"Transcript search error: {e}")
        
        return results
    
    def get_context_around_timestamp(self, video_id: int, timestamp: float, 
                                   context_seconds: int = 30) -> str:
        """
        Get transcript context around a specific timestamp
        """
        try:
            cursor = self.conn.execute("""
                SELECT text FROM transcript_segments 
                WHERE video_id = ? 
                AND start_time >= ? AND end_time <= ?
                ORDER BY start_time
            """, (video_id, timestamp - context_seconds, timestamp + context_seconds))
            
            context_segments = [row[0] for row in cursor.fetchall()]
            return ' '.join(context_segments)
            
        except Exception as e:
            logger.error(f"Error getting context: {e}")
            return ""
    
    def get_video_stats(self, video_id: int) -> Dict:
        """Get indexing statistics for a video"""
        try:
            cursor = self.conn.execute("""
                SELECT COUNT(*), SUM(word_count), MIN(start_time), MAX(end_time)
                FROM transcript_segments 
                WHERE video_id = ?
            """, (video_id,))
            
            row = cursor.fetchone()
            if row and row[0] > 0:
                return {
                    "segments": row[0],
                    "total_words": row[1] or 0,
                    "duration": (row[3] or 0) - (row[2] or 0),
                    "indexed": True
                }
            else:
                return {"indexed": False}
                
        except Exception as e:
            logger.error(f"Error getting video stats: {e}")
            return {"indexed": False, "error": str(e)}
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

# Global instance
fast_transcript_search = FastTranscriptSearch()
