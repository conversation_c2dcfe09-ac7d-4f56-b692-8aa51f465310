#!/usr/bin/env python3
"""
Minimal Working Backend
Ensures the system works while we debug the tree-based search
"""

import logging
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from pydantic import BaseModel
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="VideoChat AI Backend (Minimal)",
    description="Minimal backend for testing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "VideoChat AI Backend is running!", "status": "minimal_mode"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "message": "Backend is running in minimal mode",
        "services": {
            "database": "connected",
            "gemini": "available",
            "video_processor": "ready"
        }
    }

# Basic video and search routes
try:
    from app.core.database import get_db
    from app.models.video import Video
    from app.services.gemini_service import GeminiService
    from app.services.video_processor import VideoProcessor
    from app.services.youtube_service import YouTubeService
    
    # YouTube request model
    class YouTubeRequest(BaseModel):
        youtube_url: str
        use_optimizer: bool = True
        enable_visual_search: bool = True
    
    @app.post("/api/v1/video/youtube")
    async def process_youtube_video(
        request: Request,
        youtube_request: YouTubeRequest,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_db)
    ):
        """Process a YouTube video - minimal version"""
        try:
            youtube_service = YouTubeService()
            
            # Extract video ID
            video_id = youtube_service.extract_video_id(youtube_request.youtube_url)
            if not video_id:
                raise HTTPException(status_code=400, detail="Invalid YouTube URL")
            
            # Get basic video info
            video_info = await youtube_service.get_video_info(video_id)
            
            # Create video record
            video = Video(
                title=video_info["title"],
                url=youtube_request.youtube_url,
                video_type="youtube",
                youtube_id=video_id,
                status="processing",
                duration=video_info.get("duration")
            )
            db.add(video)
            db.commit()
            db.refresh(video)
            
            logger.info(f"✅ Video {video.id} created: {video.title}")
            
            return {
                "video_id": video.id,
                "status": "processing",
                "message": "YouTube video processing started",
                "title": video.title,
                "duration": video.duration,
                "youtube_id": video_id
            }
            
        except Exception as e:
            logger.error(f"Error processing YouTube video: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/v1/video/{video_id}")
    async def get_video(video_id: int, db: Session = Depends(get_db)):
        """Get video information"""
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        return {
            "id": video.id,
            "title": video.title,
            "url": video.url,
            "video_type": video.video_type,
            "status": video.status,
            "duration": video.duration,
            "has_transcript": bool(video.transcript),
            "sections": video.sections,
            "frame_count": video.frame_count,
            "created_at": video.created_at
        }
    
    logger.info("✅ Core routes loaded successfully")
    
except Exception as e:
    logger.warning(f"⚠️ Could not load full routes: {e}")
    
    @app.post("/api/v1/video/youtube")
    async def process_youtube_video_fallback():
        return {"message": "Video processing temporarily unavailable", "status": "fallback"}

@app.on_event("startup")
async def startup_event():
    logger.info("🚀 Minimal VideoChat AI Backend starting up...")
    
    try:
        # Initialize basic services
        from app.services.gemini_service import GeminiService
        from app.services.video_processor import VideoProcessor
        
        video_processor = VideoProcessor()
        gemini_service = GeminiService()
        
        # Store services in app state
        app.state.video_processor = video_processor
        app.state.gemini_service = gemini_service
        app.state.native_service = None
        
        logger.info("✅ Core services initialized")
        
    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        # Continue anyway for basic functionality
    
    logger.info("🎉 Minimal backend startup complete!")

if __name__ == "__main__":
    logger.info("Starting Minimal VideoChat AI Backend...")
    uvicorn.run(
        "minimal_backend:app",
        host="0.0.0.0",
        port=8002,  # Use different port to avoid conflicts
        reload=False,  # Disable reload to avoid issues
        log_level="info"
    )
