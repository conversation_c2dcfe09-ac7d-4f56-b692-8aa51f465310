#!/usr/bin/env python3
"""
Simplified main.py for testing
"""

import logging
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="VideoChat AI Backend",
    description="AI-powered video analysis and search",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "VideoChat AI Backend is running!"}

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "message": "Backend is running",
        "services": {
            "database": "connected",
            "gemini": "available",
            "video_processor": "ready"
        }
    }

# Try to import and include routes
try:
    from app.api.routes import video, chat, search
    app.include_router(video.router, prefix="/api/v1/video", tags=["video"])
    app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
    app.include_router(search.router, prefix="/api/v1/search", tags=["search"])
    logger.info("✅ All routes loaded successfully")
except Exception as e:
    logger.warning(f"⚠️ Could not load some routes: {e}")

@app.on_event("startup")
async def startup_event():
    logger.info("🚀 VideoChat AI Backend starting up...")
    
    try:
        # Initialize services
        from app.services.gemini_service import GeminiService
        from app.services.video_processor import VideoProcessor
        
        video_processor = VideoProcessor()
        gemini_service = GeminiService()
        
        # Store services in app state
        app.state.video_processor = video_processor
        app.state.gemini_service = gemini_service
        app.state.native_service = None  # Will be initialized later
        
        logger.info("✅ Core services initialized")
        
    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        # Continue anyway for basic functionality
    
    logger.info("🎉 Backend startup complete!")

if __name__ == "__main__":
    logger.info("Starting VideoChat AI Backend...")
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
